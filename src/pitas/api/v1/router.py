"""API v1 router configuration."""

from fastapi import APIRouter

from pitas.api.v1.endpoints import health

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, tags=["health"])

# TODO: Add other endpoint routers as they are implemented
# api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
# api_router.include_router(users.router, prefix="/users", tags=["users"])
# api_router.include_router(vulnerabilities.router, prefix="/vulnerabilities", tags=["vulnerabilities"])
# api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
# api_router.include_router(teams.router, prefix="/teams", tags=["teams"])
# api_router.include_router(assessments.router, prefix="/assessments", tags=["assessments"])
# api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
# api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
# api_router.include_router(compliance.router, prefix="/compliance", tags=["compliance"])
# api_router.include_router(training.router, prefix="/training", tags=["training"])