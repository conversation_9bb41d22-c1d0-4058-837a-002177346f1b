"""Common dependencies for API endpoints."""

from typing import Async<PERSON>enerator
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.session import get_db
from pitas.core.security import verify_token
from pitas.core.config import settings


async def get_current_user_id(
    token: str = Depends(verify_token),
) -> str:
    """Get current authenticated user ID.

    Args:
        token: Verified JWT token subject

    Returns:
        str: User ID from token
    """
    return token


async def get_admin_user(
    current_user_id: str = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db),
) -> str:
    """Get current user if they have admin privileges.

    Args:
        current_user_id: Current user ID
        db: Database session

    Returns:
        str: Admin user ID

    Raises:
        HTTPException: If user is not admin
    """
    # TODO: Implement admin check logic
    # For now, just return the user ID
    return current_user_id


def get_rate_limiter():
    """Get rate limiter for endpoints."""
    from slowapi import Limiter
    from slowapi.util import get_remote_address

    return Limiter(
        key_func=get_remote_address,
        default_limits=[f"{settings.rate_limit_per_minute}/minute"]
    )